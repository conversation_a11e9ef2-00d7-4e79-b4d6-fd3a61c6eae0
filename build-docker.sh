#!/bin/bash

# Build Docker image for CodeForge
echo "Building CodeForge Ubuntu container image..."

# Check if Docker is running
if ! systemctl is-active --quiet docker; then
    echo "Docker is not running. Please start Docker first."
    exit 1
fi

# Build the Docker image
echo "Building codeforge_ubuntu:latest from Dockerfile.ubuntu..."
cd backend

# Build with sudo if needed
if docker build -t codeforge_ubuntu:latest -f Dockerfile.ubuntu . 2>/dev/null; then
    echo "✅ Docker image built successfully!"
else
    echo "Building with sudo..."
    sudo docker build -t codeforge_ubuntu:latest -f Dockerfile.ubuntu .
    if [ $? -eq 0 ]; then
        echo "✅ Docker image built successfully with sudo!"
    else
        echo "❌ Failed to build Docker image"
        exit 1
    fi
fi

# List the image
echo "Checking built image..."
if docker images codeforge_ubuntu:latest 2>/dev/null | grep -q codeforge_ubuntu; then
    docker images codeforge_ubuntu:latest
else
    sudo docker images codeforge_ubuntu:latest
fi

echo "✅ Docker setup complete!"
echo "You can now use the CodeForge editor with container support."
