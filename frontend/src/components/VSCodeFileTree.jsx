import { useState, useEffect, useCallback, useRef } from 'react';
import {
  FiFolder,
  FiFile,
  FiChevronRight,
  FiChevronDown,
  FiPlus,
  FiRefreshCw,
  FiMoreHorizontal
} from 'react-icons/fi';
import { containerAPI } from '../services/api';

// Utility for path normalization
const joinPath = (...segments) => {
  const isAbsolute = segments[0] && segments[0].startsWith('/');
  const cleanSegments = segments
    .map(segment => segment.replace(/^\/+|\/+$/g, ''))
    .filter(segment => segment);

  const joined = cleanSegments.join('/');
  return isAbsolute ? `/${joined}` : joined;
};

// Context Menu Component
const ContextMenu = ({ contextMenu, onClose, onCreateFile, onCreateFolder, onDelete, onRename }) => {
  const menuRef = useRef(null);
  useEffect(() => {
    const menu = menuRef.current;
    if (menu) {
      const rect = menu.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      let { x, y } = contextMenu;

      if (x + rect.width > viewportWidth) x = viewportWidth - rect.width - 10;
      if (y + rect.height > viewportHeight) y = viewportHeight - rect.height - 10;

      menu.style.left = `${x}px`;
      menu.style.top = `${y}px`;
    }
  }, [contextMenu]);

  if (!contextMenu) return null;

  return (
    <>
      <div className="fixed inset-0 z-40" onClick={onClose} />
      <div
        ref={menuRef}
        className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg py-1 min-w-32"
        style={{ left: contextMenu.x, top: contextMenu.y }}
      >
        <button
          onClick={() => { onCreateFile(contextMenu.item.path); onClose(); }}
          className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          New File
        </button>
        <button
          onClick={() => { onCreateFolder(contextMenu.item.path); onClose(); }}
          className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          New Folder
        </button>
        <button
          onClick={() => { onRename(contextMenu.item); onClose(); }}
          className="block w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          Rename
        </button>
        <button
          onClick={() => { onDelete(contextMenu.item); onClose(); }}
          className="block w-full text-left px-3 py-1.5 text-xs text-red-600 hover:bg-red-100 dark:hover:bg-red-900"
        >
          Delete
        </button>
      </div>
    </>
  );
};

const VSCodeFileTree = ({ onFileSelect, currentFile, basePath = '/home/<USER>' }) => {
  const [treeData, setTreeData] = useState([]);
  const [expandedFolders, setExpandedFolders] = useState(new Set([basePath]));
  const [loadingFolders, setLoadingFolders] = useState(new Set());
  const [loading, setLoading] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);
  const [containerError, setContainerError] = useState(null);

  // Define parseDirectoryListing function
  const parseDirectoryListing = useCallback((output, targetPath = basePath) => {
    const cleanOutput = output.replace(/\$ $/, '').trim();
    const lines = cleanOutput.split('\n').filter(line => {
      const trimmed = line.trim();
      return trimmed &&
             !trimmed.includes('$') &&
             !trimmed.startsWith('total') &&
             !trimmed.includes('FOLDER_NOT_FOUND') &&
             !trimmed.includes('DIRECTORY_NOT_FOUND') &&
             trimmed.match(/^[drwx-]{10}/);
    });

    const items = [];
    const seenNames = new Set();

    lines.forEach(line => {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 9) {
        const permissions = parts[0];
        const name = parts.slice(8).join(' ');
        if (name && name !== '.' && name !== '..' && !name.startsWith('.') && !seenNames.has(name)) {
          seenNames.add(name);
          const isDirectory = permissions.startsWith('d');
          const itemPath = joinPath(targetPath, name);
          items.push({
            name,
            path: itemPath,
            type: isDirectory ? 'folder' : 'file',
            children: isDirectory ? null : undefined,
            level: 0
          });
        }
      }
    });

    return items.sort((a, b) => (a.type !== b.type ? (a.type === 'folder' ? -1 : 1) : a.name.localeCompare(b.name)));
  }, [basePath]);

  // Define loadFileTree function
  const loadFileTree = useCallback(async () => {
    console.log('🌳 VSCodeFileTree: Starting to load file tree for basePath:', basePath);
    setLoading(true);
    setTreeData([]);
    try {
      console.log('🌳 VSCodeFileTree: Executing command:', `ls -la "${basePath}"`);
      const result = await containerAPI.executeCommand(
        `ls -la "${basePath}" 2>/dev/null || echo "DIRECTORY_NOT_FOUND"`
      );
      console.log('🌳 VSCodeFileTree: API result:', result);

      if (!result.success) {
        const errorType = result.error_type || 'unknown';
        if (errorType === 'no_container' || errorType === 'docker_unavailable') {
          console.warn('🌳 VSCodeFileTree: Container not available');
          setContainerError({
            type: errorType,
            message: result.error || 'Development environment not available'
          });
          setTreeData([]);
          return;
        }
        throw new Error(result.error || 'Failed to fetch directory listing');
      }
      if (result.data.output.includes('DIRECTORY_NOT_FOUND') || result.data.output.includes('No such file or directory')) {
        console.log('🌳 VSCodeFileTree: Directory not found');
        setTreeData([]);
        console.warn(`Directory "${basePath}" not found or inaccessible.`);
        return;
      }
      const tree = parseDirectoryListing(result.data.output, basePath);
      console.log('🌳 VSCodeFileTree: Parsed tree:', tree);
      setTreeData(tree);
      setContainerError(null); // Clear any previous container errors
    } catch (error) {
      console.error('🌳 VSCodeFileTree: Error loading file tree:', error);
      console.warn(`Failed to load file tree: ${error.message}`);
      setTreeData([]);
    } finally {
      setLoading(false);
    }
  }, [basePath, parseDirectoryListing]);

  useEffect(() => {
    console.log('🌳 VSCodeFileTree: Component mounted/basePath changed:', basePath);
    const saved = localStorage.getItem('expandedFolders');
    if (saved) {
      try {
        setExpandedFolders(new Set(JSON.parse(saved)));
      } catch (error) {
        console.warn('Failed to parse saved expanded folders:', error);
        setExpandedFolders(new Set([basePath]));
      }
    }
    console.log('🌳 VSCodeFileTree: Calling loadFileTree...');
    loadFileTree();
  }, [basePath, loadFileTree]);

  useEffect(() => {
    localStorage.setItem('expandedFolders', JSON.stringify([...expandedFolders]));
  }, [expandedFolders]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        createNewFile();
      }
      if (e.ctrlKey && e.shiftKey && e.key === 'N') {
        e.preventDefault();
        createNewFolder();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getFolderIcon = (folderName) => {
    const folderIconMap = {
      'src': '📁',
      'components': '🧩',
      'pages': '📄',
      'utils': '🔧',
      'assets': '🖼️',
      'images': '🖼️',
      'styles': '🎨',
      'css': '🎨',
      'js': '📜',
      'api': '🌐',
      'services': '⚙️',
      'hooks': '🪝',
      'context': '🔗',
      'lib': '📚',
      'public': '🌍',
      'build': '🏗️',
      'dist': '📦',
      'node_modules': '📦',
      'tests': '🧪',
      'test': '🧪',
      '__tests__': '🧪',
      'docs': '📖',
      'config': '⚙️',
      '.git': '🔀',
      '.vscode': '💙',
    };
    return folderIconMap[folderName.toLowerCase()] || null;
  };

  const getFileIcon = (fileName, isFolder = false) => {
    if (isFolder) return getFolderIcon(fileName);
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    const iconMap = {
      'js': { icon: '📄', color: '#f7df1e' },
      'jsx': { icon: '⚛️', color: '#61dafb' },
      'ts': { icon: '📘', color: '#3178c6' },
      'tsx': { icon: '⚛️', color: '#61dafb' },
      'html': { icon: '🌐', color: '#e34f26' },
      'css': { icon: '🎨', color: '#1572b6' },
      'scss': { icon: '🎨', color: '#cf649a' },
      'sass': { icon: '🎨', color: '#cf649a' },
      'json': { icon: '📋', color: '#000000' },
      'xml': { icon: '📄', color: '#ff6600' },
      'yml': { icon: '📄', color: '#cb171e' },
      'yaml': { icon: '📄', color: '#cb171e' },
      'md': { icon: '📝', color: '#000000' },
      'txt': { icon: '📄', color: '#000000' },
      'pdf': { icon: '📕', color: '#ff0000' },
      'py': { icon: '🐍', color: '#3776ab' },
      'java': { icon: '☕', color: '#ed8b00' },
      'cpp': { icon: '⚙️', color: '#00599c' },
      'c': { icon: '⚙️', color: '#a8b9cc' },
      'php': { icon: '🐘', color: '#777bb4' },
      'rb': { icon: '💎', color: '#cc342d' },
      'go': { icon: '🐹', color: '#00add8' },
      'rs': { icon: '🦀', color: '#000000' },
      'vue': { icon: '💚', color: '#4fc08d' },
      'svelte': { icon: '🧡', color: '#ff3e00' },
      'png': { icon: '🖼️', color: '#000000' },
      'jpg': { icon: '🖼️', color: '#000000' },
      'jpeg': { icon: '🖼️', color: '#000000' },
      'gif': { icon: '🖼️', color: '#000000' },
      'svg': { icon: '🎨', color: '#ffb13b' },
      'env': { icon: '🔧', color: '#faf047' },
      'gitignore': { icon: '🚫', color: '#f14e32' },
      'dockerfile': { icon: '🐳', color: '#2496ed' },
      'lock': { icon: '🔒', color: '#000000' },
      'config': { icon: '⚙️', color: '#6c6c6c' },
    };
    
    return iconMap[ext] || { icon: '📄', color: '#6c6c6c' };
  };

  const getDefaultContent = (fileName) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'jsx':
        return `// ${fileName}\nimport React from 'react';\n\nconst Component = () => {\n  return (\n    <div>\n      <h1>Hello from ${fileName}!</h1>\n    </div>\n  );\n};\n\nexport default Component;\n`;
      case 'html':
        return `<!DOCTYPE html>\n<html lang="en">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <title>${fileName.replace('.html', '')}</title>\n</head>\n<body>\n  <h1>Welcome to ${fileName}</h1>\n</body>\n</html>\n`;
      case 'css':
        return `/* ${fileName} */\nbody {\n  font-family: Arial, sans-serif;\n  margin: 0;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\nh1 {\n  color: #333;\n  text-align: center;\n}\n`;
      case 'json':
        return `{\n  "name": "${fileName.replace('.json', '')}",\n  "version": "1.0.0",\n  "description": "Generated by CodeForge"\n}\n`;
      case 'md':
        return `# ${fileName.replace('.md', '')}\n\nWelcome to your new markdown file!\n\n## Features\n\n- Easy to edit\n- Great for documentation\n- Supports formatting\n\n**Created with CodeForge** 🚀\n`;
      case 'py':
        return `# ${fileName}\nprint("Hello from ${fileName}!")\n\ndef main():\n    """Main function"""\n    print("Welcome to Python!")\n\nif __name__ == "__main__":\n    main()\n`;
      default:
        return `// ${fileName}\nconsole.log("Hello from ${fileName}!");\n`;
    }
  };





  const loadFolderContents = async (folderPath) => {
    try {
      setLoadingFolders(prev => new Set([...prev, folderPath]));
      const result = await containerAPI.executeCommand(
        `ls -la "${folderPath}" 2>/dev/null || echo "FOLDER_NOT_FOUND"`
      );

      if (result.success && result.data && result.data.output) {
        const output = result.data.output;
        if (!output.includes('FOLDER_NOT_FOUND') && !output.includes('No such file or directory')) {
          const folderItems = parseDirectoryListing(output, folderPath);
          setTreeData(prevTree => updateTreeWithFolderContents(prevTree, folderPath, folderItems));
        } else {
          setTreeData(prevTree => updateTreeWithFolderContents(prevTree, folderPath, []));
        }
      } else {
        throw new Error(result.error || 'Failed to fetch folder contents');
      }
    } catch (error) {
      console.error('Failed to load folder contents:', error);
      console.error(`Failed to load folder ${folderPath}: ${error.message}`);
      setTreeData(prevTree => updateTreeWithFolderContents(prevTree, folderPath, []));
    } finally {
      setLoadingFolders(prev => {
        const newSet = new Set(prev);
        newSet.delete(folderPath);
        return newSet;
      });
    }
  };

  const updateTreeWithFolderContents = (tree, folderPath, newItems) => {
    const newTree = [...tree];
    const updateNode = (nodes) => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].path === folderPath) {
          nodes[i] = {
            ...nodes[i],
            children: newItems.map(item => ({ ...item, level: nodes[i].level + 1 }))
          };
          return true;
        }
        if (nodes[i].children && nodes[i].children.length > 0) {
          nodes[i] = { ...nodes[i], children: [...nodes[i].children] };
          if (updateNode(nodes[i].children)) return true;
        }
      }
      return false;
    };
    updateNode(newTree);
    return newTree;
  };

  const toggleFolder = async (path) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
      setExpandedFolders(newExpanded);
    } else {
      setLoadingFolders(prev => new Set([...prev, path]));
      try {
        const item = treeData.find(i => i.path === path) || findItemByPath(treeData, path);
        if (item && item.children === null) await loadFolderContents(path);
        newExpanded.add(path);
        setExpandedFolders(newExpanded);
      } catch (error) {
        console.error('Failed to expand folder:', error);
      } finally {
        setLoadingFolders(prev => {
          const newSet = new Set(prev);
          newSet.delete(path);
          return newSet;
        });
      }
    }
  };

  const findItemByPath = (items, targetPath) => {
    for (const item of items) {
      if (item.path === targetPath) return item;
      if (item.children && item.children.length > 0) {
        const found = findItemByPath(item.children, targetPath);
        if (found) return found;
      }
    }
    return null;
  };

  const handleItemClick = (item) => {
    if (item.type === 'folder') {
      toggleFolder(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const handleContextMenu = (e, item) => {
    e.preventDefault();
    setContextMenu({ x: e.clientX, y: e.clientY, item });
  };

  const createNewFile = async () => {
    const fileName = prompt('Enter file name (e.g., app.js, index.html, style.css):');
    if (fileName && fileName.trim()) {
      try {
        const fullPath = joinPath(basePath, fileName.trim());
        const checkResult = await containerAPI.executeCommand(`[ -f "${fullPath}" ] && echo "EXISTS" || echo "NOT_EXISTS"`);
        if (checkResult.success && checkResult.data.output.includes('EXISTS')) {
          console.warn(`File "${fileName}" already exists. Please choose a different name.`);
          return;
        }
        const content = getDefaultContent(fileName);
        const encodedContent = btoa(content);
        const result = await containerAPI.executeCommand(
          `mkdir -p "${basePath}" && echo '${encodedContent}' | base64 -d > "${fullPath}"`
        );
        if (result.success) {
          loadFileTree();
          setTimeout(() => {
            onFileSelect({ name: fileName.trim(), path: fullPath, type: 'file' });
          }, 500);
        } else {
          console.error('Failed to create file. Please try again.');
        }
      } catch (error) {
        console.error('Error creating file:', error);
        console.error(`Failed to create file: ${error.message}`);
      }
    }
    setContextMenu(null);
  };

  const createNewFolder = async () => {
    const folderName = prompt('Enter folder name (e.g., components, utils, assets):');
    if (folderName && folderName.trim()) {
      try {
        const fullPath = joinPath(basePath, folderName.trim());
        const checkResult = await containerAPI.executeCommand(`[ -d "${fullPath}" ] && echo "EXISTS" || echo "NOT_EXISTS"`);
        if (checkResult.success && checkResult.data.output.includes('EXISTS')) {
          console.warn(`Folder "${folderName}" already exists. Please choose a different name.`);
          return;
        }
        const result = await containerAPI.executeCommand(`mkdir -p "${fullPath}"`);
        if (result.success) {
          loadFileTree();
        } else {
          console.error('Failed to create folder. Please try again.');
        }
      } catch (error) {
        console.error('Error creating folder:', error);
        console.error(`Failed to create folder: ${error.message}`);
      }
    }
    setContextMenu(null);
  };

  const handleCreateFile = async (parentPath) => {
    const fileName = prompt('Enter file name (e.g., component.jsx, utils.js, style.css):');
    if (fileName && fileName.trim()) {
      try {
        const fullPath = joinPath(parentPath, fileName.trim());
        const checkResult = await containerAPI.executeCommand(`[ -f "${fullPath}" ] && echo "EXISTS" || echo "NOT_EXISTS"`);
        if (checkResult.success && checkResult.data.output.includes('EXISTS')) {
          console.warn(`File "${fileName}" already exists. Please choose a different name.`);
          return;
        }
        const content = getDefaultContent(fileName);
        const encodedContent = btoa(content);
        const result = await containerAPI.executeCommand(
          `mkdir -p "${parentPath}" && echo '${encodedContent}' | base64 -d > "${fullPath}"`
        );
        if (result.success) {
          await loadFolderContents(parentPath);
          setTimeout(() => {
            onFileSelect({ name: fileName.trim(), path: fullPath, type: 'file' });
          }, 500);
        } else {
          console.error('Failed to create file. Please try again.');
        }
      } catch (error) {
        console.error('Error creating file:', error);
        console.error(`Failed to create file: ${error.message}`);
      }
    }
  };

  const handleCreateFolder = async (parentPath) => {
    const folderName = prompt('Enter folder name:');
    if (folderName && folderName.trim()) {
      try {
        const fullPath = joinPath(parentPath, folderName.trim());
        const checkResult = await containerAPI.executeCommand(`[ -d "${fullPath}" ] && echo "EXISTS" || echo "NOT_EXISTS"`);
        if (checkResult.success && checkResult.data.output.includes('EXISTS')) {
          console.warn(`Folder "${folderName}" already exists. Please choose a different name.`);
          return;
        }
        const result = await containerAPI.executeCommand(`mkdir -p "${fullPath}"`);
        if (result.success) {
          await loadFolderContents(parentPath);
        } else {
          console.error('Failed to create folder. Please try again.');
        }
      } catch (error) {
        console.error('Error creating folder:', error);
        console.error(`Failed to create folder: ${error.message}`);
      }
    }
  };

  const handleDelete = async (item) => {
    if (!window.confirm(`Are you sure you want to delete ${item.name}?`)) return;
    try {
      const command = item.type === 'folder' ? `rm -rf "${item.path}"` : `rm "${item.path}"`;
      const result = await containerAPI.executeCommand(command);
      if (result.success) {
        const parentPath = item.path.substring(0, item.path.lastIndexOf('/')) || basePath;
        if (expandedFolders.has(parentPath)) {
          await loadFolderContents(parentPath);
        } else {
          await loadFileTree();
        }
      } else {
        console.error(`Failed to delete ${item.name}. Please try again.`);
      }
    } catch (error) {
      console.error(`Error deleting ${item.name}:`, error);
      console.error(`Failed to delete ${item.name}: ${error.message}`);
    }
  };

  const handleRename = async (item) => {
    const newName = prompt(`Enter new name for ${item.name}:`, item.name);
    if (newName && newName.trim() && newName !== item.name) {
      try {
        const parentPath = item.path.substring(0, item.path.lastIndexOf('/')) || basePath;
        const newPath = joinPath(parentPath, newName.trim());
        const checkResult = await containerAPI.executeCommand(
          `[ -e "${newPath}" ] && echo "EXISTS" || echo "NOT_EXISTS"`
        );
        if (checkResult.success && checkResult.data.output.includes('EXISTS')) {
          console.warn(`"${newName}" already exists. Please choose a different name.`);
          return;
        }
        const result = await containerAPI.executeCommand(`mv "${item.path}" "${newPath}"`);
        if (result.success) {
          await loadFolderContents(parentPath);
          if (item.type === 'file' && currentFile?.path === item.path) {
            onFileSelect({ ...item, name: newName.trim(), path: newPath });
          }
        } else {
          console.error(`Failed to rename ${item.name}. Please try again.`);
        }
      } catch (error) {
        console.error(`Error renaming ${item.name}:`, error);
        console.error(`Failed to rename ${item.name}: ${error.message}`);
      }
    }
    setContextMenu(null);
  };

  const renderTreeItem = useCallback((item, depth = 0) => {
    const isExpanded = expandedFolders.has(item.path);
    const isSelected = currentFile?.path === item.path;
    const fileIcon = getFileIcon(item.name, item.type === 'folder');

    return (
      <div key={item.path}>
        <div
          className={`flex items-center h-7 cursor-pointer text-sm group relative ${
            isSelected
              ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-l-2 border-blue-500'
              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => handleItemClick(item)}
          onContextMenu={(e) => handleContextMenu(e, item)}
          onDoubleClick={() => item.type === 'folder' && toggleFolder(item.path)}
          role="treeitem"
          aria-selected={isSelected}
          aria-expanded={item.type === 'folder' ? isExpanded : undefined}
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleItemClick(item);
            }
            if (e.key === 'ArrowRight' && item.type === 'folder' && !isExpanded) {
              toggleFolder(item.path);
            }
            if (e.key === 'ArrowLeft' && item.type === 'folder' && isExpanded) {
              toggleFolder(item.path);
            }
          }}
        >
          {item.type === 'folder' && (
            <div
              className="w-4 h-4 flex items-center justify-center mr-1 flex-shrink-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
              onClick={(e) => {
                e.stopPropagation();
                toggleFolder(item.path);
              }}
            >
              {loadingFolders.has(item.path) ? (
                <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
              ) : isExpanded ? (
                <FiChevronDown size={10} className="text-gray-500" />
              ) : (
                <FiChevronRight size={10} className="text-gray-500" />
              )}
            </div>
          )}
          {item.type === 'file' && <div className="w-5 mr-1 flex-shrink-0"></div>}
          <div className="w-4 h-4 flex items-center justify-center mr-2 flex-shrink-0">
            {item.type === 'folder' ? (
              fileIcon ? (
                <span className="text-xs leading-none">{fileIcon}</span>
              ) : isExpanded ? (
                <FiFolder size={14} className="text-blue-600 dark:text-blue-400 opacity-80" />
              ) : (
                <FiFolder size={14} className="text-blue-600 dark:text-blue-400" />
              )
            ) : (
              <span className="text-xs leading-none" style={{ color: fileIcon.color }}>{fileIcon.icon}</span>
            )}
          </div>
          <span className="flex-1 truncate">{item.name}</span>
          <div className="opacity-0 group-hover:opacity-100 flex items-center ml-1">
            {item.type === 'folder' && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateFile(item.path);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  title="New File"
                >
                  <FiFile size={12} />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateFolder(item.path);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                  title="New Folder"
                >
                  <FiFolder size={12} />
                </button>
              </>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleContextMenu(e, item);
              }}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
            >
              <FiMoreHorizontal size={12} />
            </button>
          </div>
        </div>
        {item.type === 'folder' && isExpanded && (
          <div>
            {item.children === null ? (
              <div
                className="text-xs text-gray-400 italic py-1"
                style={{ paddingLeft: `${(depth + 1) * 16 + 24}px` }}
              >
                Loading...
              </div>
            ) : item.children.length > 0 ? (
              item.children.map(child => renderTreeItem(child, depth + 1))
            ) : (
              <div
                className="text-xs text-gray-400 italic py-1"
                style={{ paddingLeft: `${(depth + 1) * 16 + 24}px` }}
              >
                Empty folder
              </div>
            )}
          </div>
        )}
      </div>
    );
  }, [expandedFolders, currentFile, loadingFolders, onFileSelect]);

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900 text-sm">
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <FiFolder size={14} className="text-blue-500" />
          <span className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
            Explorer
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={createNewFile}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New File (Ctrl+N)"
          >
            <FiPlus size={12} />
          </button>
          <button
            onClick={createNewFolder}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="New Folder (Ctrl+Shift+N)"
          >
            <FiFolder size={12} />
          </button>
          <button
            onClick={loadFileTree}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded opacity-70 hover:opacity-100"
            title="Refresh Explorer"
          >
            <FiRefreshCw size={12} className={loading ? 'animate-spin' : ''} />
          </button>
        </div>
      </div>
      <div className="px-3 py-1 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
          <FiChevronDown size={12} className="mr-1" />
          <span className="font-medium">CODEFORGE</span>
        </div>
      </div>
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-500">
            <FiRefreshCw className="animate-spin mx-auto mb-2" size={16} />
            <p className="text-xs">Loading...</p>
          </div>
        ) : treeData.length > 0 ? (
          <div className="py-1">
            {treeData.map(item => renderTreeItem(item, 0))}
          </div>
        ) : containerError ? (
          <div className="p-4 text-center text-red-500">
            <div className="text-2xl mb-2">⚠️</div>
            <p className="text-xs mb-2 font-semibold">Container Unavailable</p>
            <p className="text-xs mb-3 text-gray-600 dark:text-gray-400">
              {containerError.message}
            </p>
            <p className="text-xs text-gray-500">
              {containerError.type === 'no_container'
                ? 'Please contact support to set up your development environment.'
                : 'Please contact support to resolve this issue.'}
            </p>
          </div>
        ) : (
          <div className="p-4 text-center text-gray-500">
            <p className="text-xs mb-2">No files found</p>
            <button
              onClick={createNewFile}
              className="text-xs text-blue-500 hover:text-blue-600"
            >
              Create your first file
            </button>
          </div>
        )}
      </div>
      <ContextMenu
        contextMenu={contextMenu}
        onClose={() => setContextMenu(null)}
        onCreateFile={handleCreateFile}
        onCreateFolder={handleCreateFolder}
        onDelete={handleDelete}
        onRename={handleRename}
      />
    </div>
  );
};

export default VSCodeFileTree;