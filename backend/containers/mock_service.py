"""
Mock container service for development when <PERSON><PERSON> is not available.
This allows the editor to work without requiring Dock<PERSON> setup.
"""
import os
import tempfile
import subprocess
from pathlib import Path


class MockContainerService:
    """Mock container service that uses local filesystem instead of Docker."""
    
    def __init__(self, user_id):
        self.user_id = user_id
        # Create a temporary directory for this user's "container"
        self.base_path = Path(tempfile.gettempdir()) / f"codeforge_mock_{user_id}"
        self.base_path.mkdir(exist_ok=True)
        
        # Create initial files if they don't exist
        self._setup_initial_files()
    
    def _setup_initial_files(self):
        """Create initial welcome files."""
        welcome_file = self.base_path / "welcome.txt"
        if not welcome_file.exists():
            welcome_file.write_text("Welcome to CodeForge Development Environment!\n")
        
        readme_file = self.base_path / "README.md"
        if not readme_file.exists():
            readme_content = """# CodeForge Projects

This is your personal development environment.

## Available tools:
- Python 3 with <PERSON><PERSON><PERSON>, <PERSON>lask, FastAPI
- Node.js with npm, yarn, pnpm
- Modern frameworks: React, Vue, Angular, Next.js
- Build tools: Vite, TypeScript, Webpack
- Git for version control
- Editors: Vim, Nano, Web Editor

## Quick start commands:
- npm create vite@latest my-app    # Create Vite project
- npm create react-app my-app     # Create React app
- django-admin startproject myapp # Create Django project
- python3 -m venv myenv           # Create Python virtual env

**Note: This is a mock environment for development.**
"""
            readme_file.write_text(readme_content)
        
        # Create projects directory
        projects_dir = self.base_path / "projects"
        projects_dir.mkdir(exist_ok=True)
    
    def execute_command(self, command):
        """Execute a command in the mock container (local filesystem)."""
        try:
            # Handle common file operations
            if command.startswith('ls '):
                return self._handle_ls_command(command)
            elif command.startswith('cat '):
                return self._handle_cat_command(command)
            elif command.startswith('echo ') and '>' in command:
                return self._handle_echo_command(command)
            elif command.startswith('mkdir '):
                return self._handle_mkdir_command(command)
            elif command.startswith('rm '):
                return self._handle_rm_command(command)
            elif command.startswith('mv '):
                return self._handle_mv_command(command)
            elif command.startswith('find '):
                return self._handle_find_command(command)
            elif 'base64 -d' in command:
                return self._handle_base64_command(command)
            else:
                # For other commands, try to execute them locally
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=str(self.base_path),
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                return {
                    'output': result.stdout + result.stderr,
                    'exit_code': result.returncode
                }
        except Exception as e:
            return {
                'output': f"Mock container error: {str(e)}",
                'exit_code': 1
            }
    
    def _handle_ls_command(self, command):
        """Handle ls command."""
        try:
            # Extract path from command
            parts = command.split()
            if len(parts) > 1:
                target_path = parts[-1].strip('"\'')
                if target_path.startswith('/home/<USER>'):
                    target_path = target_path.replace('/home/<USER>', str(self.base_path))
                else:
                    target_path = self.base_path / target_path.lstrip('/')
            else:
                target_path = self.base_path
            
            target_path = Path(target_path)
            if not target_path.exists():
                return {'output': 'ls: cannot access: No such file or directory', 'exit_code': 1}
            
            # Generate ls -la style output
            output_lines = []
            if target_path.is_dir():
                for item in sorted(target_path.iterdir()):
                    stat = item.stat()
                    is_dir = item.is_dir()
                    perms = 'drwxr-xr-x' if is_dir else '-rw-r--r--'
                    size = stat.st_size
                    name = item.name
                    output_lines.append(f"{perms} 1 user user {size:>8} Jan  1 12:00 {name}")
            
            return {'output': '\n'.join(output_lines), 'exit_code': 0}
        except Exception as e:
            return {'output': f"ls error: {str(e)}", 'exit_code': 1}
    
    def _handle_cat_command(self, command):
        """Handle cat command."""
        try:
            # Extract file path
            parts = command.split()
            if len(parts) < 2:
                return {'output': 'cat: missing file operand', 'exit_code': 1}
            
            file_path = parts[1].strip('"\'')
            if file_path.startswith('/home/<USER>'):
                file_path = file_path.replace('/home/<USER>', str(self.base_path))
            else:
                file_path = self.base_path / file_path.lstrip('/')
            
            file_path = Path(file_path)
            if not file_path.exists():
                return {'output': f"cat: {file_path.name}: No such file or directory", 'exit_code': 1}
            
            content = file_path.read_text()
            return {'output': content, 'exit_code': 0}
        except Exception as e:
            return {'output': f"cat error: {str(e)}", 'exit_code': 1}
    
    def _handle_echo_command(self, command):
        """Handle echo command with redirection."""
        try:
            if '>' in command:
                parts = command.split('>', 1)
                content_part = parts[0].replace('echo ', '', 1).strip()
                file_part = parts[1].strip().strip('"\'')
                
                # Handle base64 encoded content
                if content_part.startswith("'") and content_part.endswith("'"):
                    content_part = content_part[1:-1]
                
                if file_part.startswith('/home/<USER>'):
                    file_part = file_part.replace('/home/<USER>', str(self.base_path))
                else:
                    file_part = self.base_path / file_part.lstrip('/')
                
                file_path = Path(file_part)
                file_path.parent.mkdir(parents=True, exist_ok=True)
                file_path.write_text(content_part)
                
                return {'output': '', 'exit_code': 0}
            else:
                return {'output': command.replace('echo ', '', 1), 'exit_code': 0}
        except Exception as e:
            return {'output': f"echo error: {str(e)}", 'exit_code': 1}
    
    def _handle_base64_command(self, command):
        """Handle base64 decode command."""
        try:
            import base64
            # Extract the base64 content and target file
            if "echo '" in command and "' | base64 -d >" in command:
                parts = command.split("' | base64 -d >")
                b64_content = parts[0].split("echo '")[1]
                target_file = parts[1].strip().strip('"\'')
                
                if target_file.startswith('/home/<USER>'):
                    target_file = target_file.replace('/home/<USER>', str(self.base_path))
                else:
                    target_file = self.base_path / target_file.lstrip('/')
                
                # Decode base64 content
                decoded_content = base64.b64decode(b64_content).decode('utf-8')
                
                target_path = Path(target_file)
                target_path.parent.mkdir(parents=True, exist_ok=True)
                target_path.write_text(decoded_content)
                
                return {'output': '', 'exit_code': 0}
            else:
                return {'output': 'base64: invalid command format', 'exit_code': 1}
        except Exception as e:
            return {'output': f"base64 error: {str(e)}", 'exit_code': 1}
    
    def _handle_mkdir_command(self, command):
        """Handle mkdir command."""
        try:
            parts = command.split()
            if len(parts) < 2:
                return {'output': 'mkdir: missing operand', 'exit_code': 1}
            
            dir_path = parts[-1].strip('"\'')
            if dir_path.startswith('/home/<USER>'):
                dir_path = dir_path.replace('/home/<USER>', str(self.base_path))
            else:
                dir_path = self.base_path / dir_path.lstrip('/')
            
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            return {'output': '', 'exit_code': 0}
        except Exception as e:
            return {'output': f"mkdir error: {str(e)}", 'exit_code': 1}
    
    def _handle_rm_command(self, command):
        """Handle rm command."""
        try:
            parts = command.split()
            if len(parts) < 2:
                return {'output': 'rm: missing operand', 'exit_code': 1}
            
            target_path = parts[-1].strip('"\'')
            if target_path.startswith('/home/<USER>'):
                target_path = target_path.replace('/home/<USER>', str(self.base_path))
            else:
                target_path = self.base_path / target_path.lstrip('/')
            
            target_path = Path(target_path)
            if target_path.exists():
                if target_path.is_dir():
                    import shutil
                    shutil.rmtree(target_path)
                else:
                    target_path.unlink()
            
            return {'output': '', 'exit_code': 0}
        except Exception as e:
            return {'output': f"rm error: {str(e)}", 'exit_code': 1}
    
    def _handle_mv_command(self, command):
        """Handle mv command."""
        try:
            parts = command.split()
            if len(parts) < 3:
                return {'output': 'mv: missing operand', 'exit_code': 1}
            
            src_path = parts[1].strip('"\'')
            dst_path = parts[2].strip('"\'')
            
            if src_path.startswith('/home/<USER>'):
                src_path = src_path.replace('/home/<USER>', str(self.base_path))
            else:
                src_path = self.base_path / src_path.lstrip('/')
            
            if dst_path.startswith('/home/<USER>'):
                dst_path = dst_path.replace('/home/<USER>', str(self.base_path))
            else:
                dst_path = self.base_path / dst_path.lstrip('/')
            
            Path(src_path).rename(Path(dst_path))
            return {'output': '', 'exit_code': 0}
        except Exception as e:
            return {'output': f"mv error: {str(e)}", 'exit_code': 1}
    
    def _handle_find_command(self, command):
        """Handle find command."""
        try:
            # Simple find implementation
            output_lines = []
            for item in self.base_path.rglob('*'):
                if item.is_file() or item.is_dir():
                    rel_path = item.relative_to(self.base_path)
                    output_lines.append(str(rel_path))
            
            return {'output': '\n'.join(output_lines[:200]), 'exit_code': 0}  # Limit to 200 items
        except Exception as e:
            return {'output': f"find error: {str(e)}", 'exit_code': 1}
    
    def get_status(self):
        """Get mock container status."""
        return {
            'status': 'running',
            'container_id': f'mock_container_{self.user_id}',
            'message': 'Mock container is running (development mode)'
        }
