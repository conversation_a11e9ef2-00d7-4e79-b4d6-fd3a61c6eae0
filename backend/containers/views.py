from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework import status
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
import docker
from .models import UserContainer
from .mock_service import MockContainerService
import os

class CustomTokenObtainPairView(TokenObtainPairView):
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        # Get user from the token response
        username = request.data.get('username')
        password = request.data.get('password')
        user = authenticate(username=username, password=password)

        if user and response.status_code == 200:
            try:
                client = docker.from_env()

                # Check if user already has a container
                container_record = UserContainer.objects.filter(user=user).first()
                if not container_record:
                    # Create a volume for persistent data
                    volume_name = f"codeforge_{user.id}_data"
                    try:
                        client.volumes.create(name=volume_name)
                    except docker.errors.APIError:
                        # Volume might already exist
                        pass

                    # Spin up a new Ubuntu container
                    container = client.containers.run(
                        'codeforge_ubuntu:latest',
                        command='tail -f /dev/null',  # Keep container running
                        detach=True,
                        volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                        name=f'codeforge_user_{user.id}',
                        remove=False
                    )
                    UserContainer.objects.create(
                        user=user,
                        container_id=container.id,
                        volume_name=volume_name
                    )
            except Exception as e:
                # Log error but don't fail login
                print(f"Container setup error for user {user.username}: {e}")

        return response

class SignupView(APIView):
    def post(self, request):
        try:
            username = request.data.get('username')
            email = request.data.get('email')
            password = request.data.get('password')

            # Validate required fields
            if not username or not email or not password:
                return Response({
                    'error': 'Username, email, and password are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if user already exists
            if User.objects.filter(username=username).exists():
                return Response({
                    'error': 'Username already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            if User.objects.filter(email=email).exists():
                return Response({
                    'error': 'Email already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Setup container for new user
            try:
                client = docker.from_env()

                # Create a volume for persistent data
                volume_name = f"codeforge_{user.id}_data"
                try:
                    client.volumes.create(name=volume_name)
                except docker.errors.APIError:
                    # Volume might already exist
                    pass

                # Spin up a new Ubuntu container
                container = client.containers.run(
                    'codeforge_ubuntu:latest',
                    command='tail -f /dev/null',  # Keep container running
                    detach=True,
                    volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                    name=f'codeforge_user_{user.id}',
                    remove=False
                )

                UserContainer.objects.create(
                    user=user,
                    container_id=container.id,
                    volume_name=volume_name
                )
            except Exception as e:
                # Log error but don't fail signup
                print(f"Container setup error for user {user.username}: {e}")

            return Response({
                'message': 'User created successfully',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(access_token)
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Signup failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ContainerStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # Check if we should use mock service (for development)
            use_mock = os.getenv('USE_MOCK_CONTAINER', 'false').lower() == 'true'

            if use_mock:
                mock_service = MockContainerService(request.user.id)
                return Response(mock_service.get_status())

            container_record = UserContainer.objects.filter(user=request.user).first()
            if not container_record:
                # Try to use mock service as fallback
                mock_service = MockContainerService(request.user.id)
                return Response(mock_service.get_status())

            try:
                client = docker.from_env()
            except Exception as docker_error:
                # Fallback to mock service
                mock_service = MockContainerService(request.user.id)
                return Response(mock_service.get_status())

            try:
                container = client.containers.get(container_record.container_id)
                return Response({
                    'status': container.status,
                    'container_id': container_record.container_id,
                    'created_at': container_record.created_at,
                    'message': f'Container is {container.status}'
                })
            except docker.errors.NotFound:
                # Container not found, use mock service
                mock_service = MockContainerService(request.user.id)
                status_data = mock_service.get_status()
                status_data['message'] = 'Using mock container (development mode)'
                return Response(status_data)
            except Exception as container_error:
                # Fallback to mock service
                mock_service = MockContainerService(request.user.id)
                return Response(mock_service.get_status())

        except Exception as e:
            # Final fallback to mock service
            try:
                mock_service = MockContainerService(request.user.id)
                return Response(mock_service.get_status())
            except:
                return Response({
                    'status': 'error',
                    'message': 'An unexpected error occurred.',
                    'error_type': 'unexpected_error',
                    'details': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ExecuteCommandView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            command = request.data.get('command', '')
            if not command:
                return Response({'error': 'Command is required'}, status=status.HTTP_400_BAD_REQUEST)

            # Check if we should use mock service (for development)
            use_mock = os.getenv('USE_MOCK_CONTAINER', 'false').lower() == 'true'

            if use_mock:
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)

            container_record = UserContainer.objects.filter(user=request.user).first()
            if not container_record:
                # Fallback to mock service
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)

            try:
                client = docker.from_env()
            except Exception as docker_error:
                # Fallback to mock service
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)

            try:
                container = client.containers.get(container_record.container_id)

                # Check if container is running
                if container.status != 'running':
                    try:
                        container.start()
                    except Exception as start_error:
                        # Fallback to mock service
                        mock_service = MockContainerService(request.user.id)
                        result = mock_service.execute_command(command)
                        return Response(result)

                # Execute command in real container
                try:
                    result = container.exec_run(
                        cmd=command,
                        tty=True,
                        environment={'TERM': 'xterm-256color'},
                        workdir='/home/<USER>'
                    )

                    return Response({
                        'output': result.output.decode('utf-8', errors='ignore'),
                        'exit_code': result.exit_code
                    })
                except Exception as exec_error:
                    # Fallback to mock service
                    mock_service = MockContainerService(request.user.id)
                    result = mock_service.execute_command(command)
                    return Response(result)

            except docker.errors.NotFound:
                # Container not found, use mock service
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)
            except Exception as container_error:
                # Fallback to mock service
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)



        except Exception as e:
            # Final fallback to mock service
            try:
                mock_service = MockContainerService(request.user.id)
                result = mock_service.execute_command(command)
                return Response(result)
            except:
                return Response({
                    'error': 'An unexpected error occurred while processing your request.',
                    'error_type': 'unexpected_error',
                    'details': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)