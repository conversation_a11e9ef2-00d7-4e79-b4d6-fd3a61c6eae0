from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework import status
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
import docker
from .models import UserContainer
from .mock_service import MockContainerService
import os

class CustomTokenObtainPairView(TokenObtainPairView):
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        # Get user from the token response
        username = request.data.get('username')
        password = request.data.get('password')
        user = authenticate(username=username, password=password)

        if user and response.status_code == 200:
            try:
                client = docker.from_env()

                # Check if user already has a container
                container_record = UserContainer.objects.filter(user=user).first()
                if not container_record:
                    # Create a volume for persistent data
                    volume_name = f"codeforge_{user.id}_data"
                    try:
                        client.volumes.create(name=volume_name)
                    except docker.errors.APIError:
                        # Volume might already exist
                        pass

                    # Spin up a new Ubuntu container
                    container = client.containers.run(
                        'codeforge_ubuntu:latest',
                        command='tail -f /dev/null',  # Keep container running
                        detach=True,
                        volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                        name=f'codeforge_user_{user.id}',
                        remove=False
                    )
                    UserContainer.objects.create(
                        user=user,
                        container_id=container.id,
                        volume_name=volume_name
                    )
            except Exception as e:
                # Log error but don't fail login
                print(f"Container setup error for user {user.username}: {e}")

        return response

class SignupView(APIView):
    def post(self, request):
        try:
            username = request.data.get('username')
            email = request.data.get('email')
            password = request.data.get('password')

            # Validate required fields
            if not username or not email or not password:
                return Response({
                    'error': 'Username, email, and password are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if user already exists
            if User.objects.filter(username=username).exists():
                return Response({
                    'error': 'Username already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            if User.objects.filter(email=email).exists():
                return Response({
                    'error': 'Email already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )

            # Generate tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Setup container for new user
            try:
                client = docker.from_env()

                # Create a volume for persistent data
                volume_name = f"codeforge_{user.id}_data"
                try:
                    client.volumes.create(name=volume_name)
                except docker.errors.APIError:
                    # Volume might already exist
                    pass

                # Spin up a new Ubuntu container
                container = client.containers.run(
                    'codeforge_ubuntu:latest',
                    command='tail -f /dev/null',  # Keep container running
                    detach=True,
                    volumes={volume_name: {'bind': '/home/<USER>', 'mode': 'rw'}},
                    name=f'codeforge_user_{user.id}',
                    remove=False
                )

                UserContainer.objects.create(
                    user=user,
                    container_id=container.id,
                    volume_name=volume_name
                )
            except Exception as e:
                # Log error but don't fail signup
                print(f"Container setup error for user {user.username}: {e}")

            return Response({
                'message': 'User created successfully',
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(access_token)
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'error': f'Signup failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ContainerStatusView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            container_record = UserContainer.objects.filter(user=request.user).first()
            if not container_record:
                return Response({
                    'status': 'no_container',
                    'message': 'No development container found. Please contact support to set up your environment.',
                    'error_type': 'no_container'
                }, status=status.HTTP_404_NOT_FOUND)

            try:
                client = docker.from_env()
            except Exception as docker_error:
                return Response({
                    'status': 'docker_unavailable',
                    'message': 'Docker service is not available.',
                    'error_type': 'docker_unavailable',
                    'details': str(docker_error)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            try:
                container = client.containers.get(container_record.container_id)
                return Response({
                    'status': container.status,
                    'container_id': container_record.container_id,
                    'created_at': container_record.created_at,
                    'message': f'Container is {container.status}'
                })
            except docker.errors.NotFound:
                return Response({
                    'status': 'container_not_found',
                    'message': 'Your development container was not found.',
                    'error_type': 'container_not_found'
                }, status=status.HTTP_404_NOT_FOUND)
            except Exception as container_error:
                return Response({
                    'status': 'container_error',
                    'message': 'Failed to access your development container.',
                    'error_type': 'container_access_error',
                    'details': str(container_error)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': 'An unexpected error occurred.',
                'error_type': 'unexpected_error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ExecuteCommandView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            command = request.data.get('command', '')
            if not command:
                return Response({'error': 'Command is required'}, status=status.HTTP_400_BAD_REQUEST)

            container_record = UserContainer.objects.filter(user=request.user).first()
            if not container_record:
                return Response({
                    'error': 'No container found. Please contact support to set up your development environment.',
                    'error_type': 'no_container'
                }, status=status.HTTP_404_NOT_FOUND)

            try:
                client = docker.from_env()
            except Exception as docker_error:
                return Response({
                    'error': 'Docker service is not available. Please contact support.',
                    'error_type': 'docker_unavailable',
                    'details': str(docker_error)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            try:
                container = client.containers.get(container_record.container_id)
            except docker.errors.NotFound:
                return Response({
                    'error': 'Your development container was not found. Please contact support to recreate it.',
                    'error_type': 'container_not_found'
                }, status=status.HTTP_404_NOT_FOUND)
            except Exception as container_error:
                return Response({
                    'error': 'Failed to access your development container.',
                    'error_type': 'container_access_error',
                    'details': str(container_error)
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            # Check if container is running
            if container.status != 'running':
                try:
                    container.start()
                except Exception as start_error:
                    return Response({
                        'error': 'Your development container is not running and could not be started.',
                        'error_type': 'container_start_failed',
                        'details': str(start_error)
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            try:
                result = container.exec_run(
                    cmd=command,
                    tty=True,
                    environment={'TERM': 'xterm-256color'},
                    workdir='/home/<USER>'
                )

                return Response({
                    'output': result.output.decode('utf-8', errors='ignore'),
                    'exit_code': result.exit_code
                })
            except Exception as exec_error:
                return Response({
                    'error': f'Failed to execute command: {command}',
                    'error_type': 'command_execution_failed',
                    'details': str(exec_error)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'error': 'An unexpected error occurred while processing your request.',
                'error_type': 'unexpected_error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)